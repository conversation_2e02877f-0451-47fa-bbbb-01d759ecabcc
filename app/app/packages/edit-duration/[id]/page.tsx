"use client";

import { useRouter, useParams } from "next/navigation";
import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import ISPdurationForm from "@/components/duration/isp-duration-form";
import { ArrowLeft } from "lucide-react";

// Sample data - in a real app, this would come from an API
const durations = [
  {
    id: 1,
    type: "duration",
    duration: "1 month",
    description: "Monthly subscription",
  },
  {
    id: 2,
    type: "duration",
    duration: "3 months",
    description: "Quarterly subscription",
  },
  {
    id: 3,
    type: "duration",
    duration: "6 months",
    description: "Half-yearly subscription",
  },
  {
    id: 4,
    type: "duration",
    duration: "12 months",
    description: "Annual subscription",
  },
];

export default function EditDurationPage() {
  const router = useRouter();
  const params = useParams();
  const durationId = params.id as string;
  
  const [durationData, setDurationData] = useState<any>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // In a real application, you would fetch the duration data from an API
    // For now, we'll simulate this with mock data
    const fetchDuration = () => {
      const id = parseInt(durationId);
      const foundDuration = durations.find(duration => duration.id === id);
      
      if (foundDuration) {
        setDurationData(foundDuration);
      }
      setLoading(false);
    };

    if (durationId) {
      fetchDuration();
    }
  }, [durationId]);

  const handleSubmit = (updatedDuration: any) => {
    // Here you would typically make an API call to update the duration
    // For now, we'll just log the updated duration and redirect back
    console.log("Duration updated:", updatedDuration);

    // TODO: Add API call here
    // Example:
    // try {
    //   await fetch(`/api/durations/${durationId}`, {
    //     method: 'PUT',
    //     headers: { 'Content-Type': 'application/json' },
    //     body: JSON.stringify(updatedDuration)
    //   });
    //   // Show success message
    //   router.push("/app/packages");
    // } catch (error) {
    //   // Handle error
    // }
    
    // For now, just redirect back to packages page
    router.push("/app/packages");
  };

  const handleCancel = () => {
    router.push("/app/packages");
  };

  if (loading) {
    return (
      <div className="p-6 space-y-6">
        <div className="flex items-center gap-4">
          <Button
            variant="outline"
            size="sm"
            onClick={handleCancel}
            className="flex items-center gap-2"
          >
            <ArrowLeft className="h-4 w-4" />
            Back to Packages
          </Button>
          <h1 className="text-2xl font-bold">Edit Duration</h1>
        </div>
        <div className="text-center py-8">Loading...</div>
      </div>
    );
  }

  if (!durationData) {
    return (
      <div className="p-6 space-y-6">
        <div className="flex items-center gap-4">
          <Button
            variant="outline"
            size="sm"
            onClick={handleCancel}
            className="flex items-center gap-2"
          >
            <ArrowLeft className="h-4 w-4" />
            Back to Packages
          </Button>
          <h1 className="text-2xl font-bold">Edit Duration</h1>
        </div>
        <div className="text-center py-8 text-red-500">Duration not found</div>
      </div>
    );
  }

  return (
    <div className="p-6 space-y-6">
      <div className="flex items-center gap-4">
        <Button
          variant="outline"
          size="sm"
          onClick={handleCancel}
          className="flex items-center gap-2"
        >
          <ArrowLeft className="h-4 w-4" />
          Back to Packages
        </Button>
        <h1 className="text-2xl font-bold">Edit Duration: {durationData.duration}</h1>
      </div>

      <div className="bg-white rounded-lg shadow-sm">
        <ISPdurationForm 
          onSubmit={handleSubmit} 
          onCancel={handleCancel}
          initialData={durationData}
          isEdit={true}
        />
      </div>
    </div>
  );
}
