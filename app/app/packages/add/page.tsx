"use client";

import { useRouter } from "next/navigation";
import { <PERSON><PERSON> } from "@/components/ui/button";
import ISPpackagesForm from "@/components/packages/isp-packages-form";
import { ArrowLeft } from "lucide-react";

export default function AddPackagePage() {
  const router = useRouter();

  const handleSubmit = (packageData: any) => {
    // Here you would typically make an API call to save the package
    // For now, we'll just log the package and redirect back
    console.log("New package created:", packageData);

    // TODO: Add API call here
    // Example:
    // try {
    //   await fetch('/api/packages', {
    //     method: 'POST',
    //     headers: { 'Content-Type': 'application/json' },
    //     body: JSON.stringify(packageData)
    //   });
    //   // Show success message
    //   router.push("/app/packages");
    // } catch (error) {
    //   // Handle error
    // }
    
    // For now, just redirect back to packages page
    router.push("/app/packages");
  };

  const handleCancel = () => {
    router.push("/app/packages");
  };

  return (
    <div className="p-6 space-y-6">
      <div className="flex items-center gap-4">
        <Button
          variant="outline"
          size="sm"
          onClick={handleCancel}
          className="flex items-center gap-2"
        >
          <ArrowLeft className="h-4 w-4" />
          Back to Packages
        </Button>
        <h1 className="text-2xl font-bold">Add New Package</h1>
      </div>

      <div className="bg-white rounded-lg shadow-sm">
        <ISPpackagesForm onSubmit={handleSubmit} onCancel={handleCancel} />
      </div>
    </div>
  );
}
