"use client";

import { useRouter } from "next/navigation";
import { <PERSON><PERSON> } from "@/components/ui/button";
import ISPdurationForm from "@/components/duration/isp-duration-form";
import { ArrowLeft } from "lucide-react";

export default function AddDurationPage() {
  const router = useRouter();

  const handleSubmit = (durationData: any) => {
    // Here you would typically make an API call to save the duration
    // For now, we'll just log the duration and redirect back
    console.log("New duration created:", durationData);

    // TODO: Add API call here
    // Example:
    // try {
    //   await fetch('/api/durations', {
    //     method: 'POST',
    //     headers: { 'Content-Type': 'application/json' },
    //     body: JSON.stringify(durationData)
    //   });
    //   // Show success message
    //   router.push("/app/packages");
    // } catch (error) {
    //   // Handle error
    // }
    
    // For now, just redirect back to packages page
    router.push("/app/packages");
  };

  const handleCancel = () => {
    router.push("/app/packages");
  };

  return (
    <div className="p-6 space-y-6">
      <div className="flex items-center gap-4">
        <Button
          variant="outline"
          size="sm"
          onClick={handleCancel}
          className="flex items-center gap-2"
        >
          <ArrowLeft className="h-4 w-4" />
          Back to Packages
        </Button>
        <h1 className="text-2xl font-bold">Add New Duration</h1>
      </div>

      <div className="bg-white rounded-lg shadow-sm">
        <ISPdurationForm onSubmit={handleSubmit} onCancel={handleCancel} />
      </div>
    </div>
  );
}
