"use client";

import React from "react";
import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { PageContainer } from "@/components/ui/page-container";
import { PageHeader } from "@/components/ui/page-header";
import { StatsCard } from "@/components/ui/stats-card";
import { StatsGrid } from "@/components/ui/stats-grid";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Search, Plus, Package, Wifi, Zap, Clock } from "lucide-react";
import Cookies from "js-cookie";
import apiClient from "@/lib/apiClient";

interface packages {
  id: number;
  type: string;
  package_name: string;
  upload: string;
  download: string;
  price: string;
}

export default function PackagesPage() {
  const router = useRouter();
  const [search, setSearch] = useState("");
  const [packagesList, setPackagesList] = useState<packages[]>([]);

  useEffect(() => {
    const fetchPackages = async () => {
      try {
        const response = await apiClient.get("/package");
        setPackagesList(response.data.data);
        console.log(response.data.data);
      } catch (error) {
        console.error("Failed to fetch packages:", error);
      }
    };

    fetchPackages();
  }, []);

  // Filter data based on search
  const filteredPackages = packagesList.filter(
    (pkg) =>
      pkg?.package_name.toLowerCase().includes(search.toLowerCase()) ||
      pkg?.price.toLowerCase().includes(search.toLowerCase()) ||
      pkg?.upload.toLowerCase().includes(search.toLowerCase()) ||
      pkg?.download.toLowerCase().includes(search.toLowerCase())
  );

  // Delete functions
  const handleDeletePackage = (id: number) => {
    if (window.confirm("Are you sure you want to delete this package?")) {
      setPackagesList(packagesList.filter((pkg) => pkg.id !== id));
    }
  };

  // Edit functions
  const handleEditPackage = (id: number) => {
    router.push(`/app/packages/edit/${id}`);
  };

  return (
    <PageContainer>
      <PageHeader
        title="Package Management"
        description="Manage internet packages and pricing plans"
      >
        <Button onClick={() => router.push("/app/packages/add")}>
          <Plus className="h-4 w-4 mr-2" />
          Add Package
        </Button>
        <Button
          onClick={() => router.push("/app/packages/add-duration")}
          variant="outline"
        >
          <Clock className="h-4 w-4 mr-2" />
          Add Duration
        </Button>
      </PageHeader>

      {/* Stats Cards */}
      <StatsGrid columns={4}>
        <StatsCard
          title="Total Clients"
          value="12,000"
          icon={<Package className="h-6 w-6 text-blue-500" />}
        />

        <StatsCard
          title="100/100 Mbps"
          value="8,000 / 12,000"
          description="Active subscriptions"
          icon={<Wifi className="h-6 w-6 text-green-500" />}
        />

        <StatsCard
          title="200/200 Mbps"
          value="3,000 / 12,000"
          description="Active subscriptions"
          icon={<Zap className="h-6 w-6 text-orange-500" />}
        />

        <StatsCard
          title="300/300 Mbps"
          value="1,000 / 12,000"
          description="Active subscriptions"
          icon={<Zap className="h-6 w-6 text-red-500" />}
        />
      </StatsGrid>

      {/* Package List */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle>Available Packages</CardTitle>
            <div className="relative">
              <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
              <input
                type="text"
                placeholder="Search packages..."
                value={search}
                onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                  setSearch(e.target.value)
                }
                className="pl-9 pr-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-ring focus:border-transparent w-[300px]"
              />
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="rounded-lg border">
            <table className="w-full">
              <thead>
                <tr className="border-b bg-muted/50">
                  <th className="h-12 px-4 text-left align-middle font-medium text-muted-foreground">
                    SN
                  </th>
                  <th className="h-12 px-4 text-left align-middle font-medium text-muted-foreground">
                    Package Name
                  </th>
                  <th className="h-12 px-4 text-left align-middle font-medium text-muted-foreground">
                    Upload Speed
                  </th>
                  <th className="h-12 px-4 text-left align-middle font-medium text-muted-foreground">
                    Download Speed
                  </th>
                  <th className="h-12 px-4 text-left align-middle font-medium text-muted-foreground">
                    Price
                  </th>
                  <th className="h-12 px-4 text-right align-middle font-medium text-muted-foreground">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody>
                {filteredPackages?.length === 0 ? (
                  <tr>
                    <td
                      colSpan={6}
                      className="h-24 text-center text-muted-foreground"
                    >
                      No packages found matching your search.
                    </td>
                  </tr>
                ) : (
                  filteredPackages?.map((pkg, index) => (
                    <tr
                      key={pkg.id}
                      className="border-b transition-colors hover:bg-muted/50"
                    >
                      <td className="p-4 align-middle">{index + 1}</td>
                      <td className="p-4 align-middle font-medium">
                        {pkg.package_name}
                      </td>
                      <td className="p-4 align-middle">
                        <Badge variant="outline">{pkg.upload}</Badge>
                      </td>
                      <td className="p-4 align-middle">
                        <Badge variant="outline">{pkg.download}</Badge>
                      </td>
                      <td className="p-4 align-middle">
                        <span className="font-medium text-green-600">
                          {pkg.price}
                        </span>
                      </td>
                      <td className="p-4 align-middle text-right">
                        <div className="flex items-center justify-end gap-2">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleEditPackage(pkg.id)}
                          >
                            Edit
                          </Button>
                          <Button
                            variant="destructive"
                            size="sm"
                            onClick={() => handleDeletePackage(pkg.id)}
                          >
                            Delete
                          </Button>
                        </div>
                      </td>
                    </tr>
                  ))
                )}
              </tbody>
            </table>
          </div>
        </CardContent>
      </Card>
    </PageContainer>
  );
}
