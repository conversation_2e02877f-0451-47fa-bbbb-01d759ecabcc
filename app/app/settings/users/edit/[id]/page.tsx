"use client";

import { useRouter, useParams } from "next/navigation";
import { useState } from "react";
import { Button } from "@/components/ui/button";
import { EditUserForm } from "@/components/settings/users/edit-user";
import { User } from "@/components/settings/users/user-management";
import { ArrowLeft } from "lucide-react";
import apiClient from "@/lib/apiClient";

export default function EditUserPage() {
  const router = useRouter();
  const params = useParams();
  const userId = params.id as string;
  const [user, setUser] = useState<User>();

  const handleSubmit = async (updatedUser: User) => {
    try { 
      const response = await apiClient.patch(`/user/${userId}`, updatedUser);
      console.log("User updated:", response.data);
    } catch (error) {
      console.error("Failed to update user:", error);
      alert("Failed to update user");
    }
    setUser(updatedUser);
    router.push("/app/settings/users");
  };

  const handleCancel = () => {
    router.push("/app/settings/users");
  };

  return (
    <div className="p-6 max-w-2xl mx-auto">
      {/* Header with back button */}
      <div className="flex items-center gap-4 mb-6">
        <Button
          variant="ghost"
          size="sm"
          onClick={handleCancel}
          className="flex items-center gap-2"
        >
          <ArrowLeft className="h-4 w-4" />
          Back to Users
        </Button>
      </div>

      {/* Page title */}
      <div className="mb-8">
        <h1 className="text-3xl font-bold">Edit User</h1>
        <p className="text-gray-600 mt-2">
          {user
            ? <>Update user information for <strong>{user.username}</strong>.</>
            : "Loading user..."}
        </p>
      </div>

      {/* Form container */}
      <div className="bg-white p-6 rounded-lg border shadow-sm">
        {user ? (
          <EditUserForm user={user} onSubmit={handleSubmit} />
        ) : (
          <div>Loading...</div>
        )}

        {/* Cancel button */}
        <div className="mt-6 pt-4 border-t">
          <Button
            variant="outline"
            onClick={handleCancel}
            className="w-full"
          >
            Cancel
          </Button>
        </div>
      </div>
    </div>
  );
}
