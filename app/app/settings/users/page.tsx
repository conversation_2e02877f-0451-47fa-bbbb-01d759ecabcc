"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { UserTable, User } from "@/components/settings/users/user-management";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { PageContainer } from "@/components/ui/page-container";
import { PageHeader } from "@/components/ui/page-header";
import { Search, Plus, Trash2 } from "lucide-react";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import apiClient from "@/lib/apiClient";

export default function UserManagementPage() {
  const router = useRouter();
  const [userData, setUser] = useState<User[]>([]);
  const [search, setSearch] = useState("");

  useEffect(() => {
    const fetchUsers = async () => {
      try {
        const response = await apiClient.get("/user");
        setUser(response.data);
      } catch (error) {
        console.error("Failed to fetch users:", error);
      }
    };

    fetchUsers();
  }, []);

  const handleDeleteUser = async (id: string) => {
    try {
      const response = await apiClient.delete(`/user/${id}`);
      console.log("User deleted:", response.data);
      setUser(userData.filter((user) => user.id !== id));
    } catch (error) {
      console.error("Failed to delete user:", error);
    }
  };

  return (
    <PageContainer>
      <PageHeader
        title="User Management"
        description="Manage user accounts, permissions, and access controls"
      >
        <div className="relative">
          <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
          <Input
            placeholder="Search users..."
            value={search}
            onChange={(e) => setSearch(e.target.value)}
            className="pl-9 w-60"
          />
        </div>
        <Button onClick={() => router.push("/app/settings/users/add")}>
          <Plus className="h-4 w-4 mr-2" />
          Add New User
        </Button>
      </PageHeader>

      <UserTable
        users={userData.filter((user) =>
          user.username.toLowerCase().includes(search.toLowerCase())
        )}
        onDelete={handleDeleteUser}
      />
    </PageContainer>
  );
}

export function DeleteUserDialog({
  username,
  onDelete,
}: {
  username: string;
  onDelete: () => void;
}) {
  const [open, setOpen] = useState(false);
  const [inputName, setInputName] = useState("");

  const handleDelete = () => {
    if (inputName === username) {
      onDelete();
      setOpen(false);
      setInputName("");
    } else {
      alert("Username does not match");
    }
  };

  const handleOpenChange = (newOpen: boolean) => {
    setOpen(newOpen);
    if (!newOpen) {
      setInputName("");
    }
  };

  return (
    <Dialog open={open} onOpenChange={handleOpenChange}>
      <DialogTrigger asChild>
        <Button variant="destructive" size="sm">
          <Trash2 className="h-4 w-4 mr-1" />
          Delete
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>Confirm Deletion</DialogTitle>
          <DialogDescription>
            Are you sure you want to delete the user <strong>{username}</strong>
            ? This action cannot be undone.
          </DialogDescription>
        </DialogHeader>
        <div className="space-y-4">
          <div>
            <label className="text-sm font-medium text-muted-foreground">
              Type the username to confirm:
            </label>
            <Input
              value={inputName}
              onChange={(e) => setInputName(e.target.value)}
              placeholder="Enter username"
              className="mt-2"
            />
          </div>
        </div>
        <DialogFooter>
          <Button variant="outline" onClick={() => setOpen(false)}>
            Cancel
          </Button>
          <Button
            variant="destructive"
            onClick={handleDelete}
            disabled={inputName !== username}
          >
            Confirm Delete
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
