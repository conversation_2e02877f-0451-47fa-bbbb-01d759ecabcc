"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import {
  GroupTable,
  group,
} from "@/components/settings/groups/group-management";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { PageContainer } from "@/components/ui/page-container";
import { PageHeader } from "@/components/ui/page-header";
import { Search, Plus } from "lucide-react";
import apiClient from "@/lib/apiClient";
import Cookies from "js-cookie";

export default function GroupManagementPage() {
  const router = useRouter();
  const [groups, setGroups] = useState<group[]>([]);
  const [search, setSearch] = useState("");
  const accessToken = Cookies.get("accessToken");

  const filteredGroups = groups.filter((group) =>
    group.name.toLowerCase().includes(search.toLowerCase())
  );

  useEffect(() => {
    const fetchGroups = async () => {
      try {
        const response = await apiClient.get("/groups", {
          headers: {
            Authorization: `Bearer ${accessToken}`,
          },
          cache: "no-store",
        });
        setGroups(response.data.data);
      } catch (error) {
        console.error("Failed to fetch groups:", error);
      }
    };

    fetchGroups();
  }, [accessToken]);

  const handleDeleteGroup = async (name: string) => {
    try {
      await apiClient.delete(`/group/${name}`, {
        headers: {
          Authorization: `Bearer ${accessToken}`,
        },
      });
      setGroups((prev) => prev.filter((group) => group.name !== name));
    } catch (error) {
      alert("Failed to delete group. Please try again.");
      console.error("Delete group error:", error);
    }
  };

  return (
    <div className="p-6 space-y-8">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold">Group Management Portal</h1>
        <input
          type="text"
          placeholder="Search groups."
          value={search}
          onChange={(e) => setSearch(e.target.value)}
          className="mb-4 w-[250px] p-2 border rounded-lg focus:outline-none focus:ring focus:border-blue-300"
        />
        <Button
          className="ml-20"
          onClick={() => router.push("/app/settings/groups/add")}
        >
          Add New Group
        </Button>
      </div>

      <GroupTable groups={filteredGroups} onDelete={handleDeleteGroup} />
    </div>
  );
}

export function DeleteGroupDialog({ name, onDelete }) {
  const [showConfirm, setShowConfirm] = useState(false);
  const [inputName, setInputName] = useState("");

  const handleDelete = () => {
    if (inputName === name) {
      onDelete(name);
      setShowConfirm(false);
    } else {
      alert("Group name does not match");
    }
  };

  useEffect(() => {
    const handleKeyDown = (e) => {
      if (e.key === "Escape") {
        setShowConfirm(false);
        setInputName("");
      }
    };

    if (showConfirm) {
      document.addEventListener("keydown", handleKeyDown);
    }

    return () => {
      document.removeEventListener("keydown", handleKeyDown);
    };
  }, [showConfirm]);

  return (
    <>
      <button
        onClick={() => setShowConfirm(true)}
        className="bg-red-600 text-white px-1 py-0 space-x-5 rounded"
      >
        Delete
      </button>

      {showConfirm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center">
          <div className="bg-white p-7 rounded w-auto">
            <h2 className="text-xl font-bold mb-4 text-center">
              Confirm Deletion
            </h2>
            <p className="mb-2 text-left">
              Are you sure you want to delete the group <strong>{name}</strong>?
            </p>
            <p className="mb-4 text-sm text-gray-600 text-left">
              Type the group name to confirm:
            </p>
            <input
              className="border px-3 py-2 w-full mb-4"
              value={inputName}
              onChange={(e) => setInputName(e.target.value)}
              placeholder="Enter group name"
            />
            <div className="flex justify-end gap-2">
              <button
                onClick={() => setShowConfirm(false)}
                className="px-4 py-2 bg-gray-300 rounded"
              >
                Cancel
              </button>
              <button
                onClick={handleDelete}
                className="px-4 py-2 bg-red-600 text-white rounded"
              >
                Confirm Delete
              </button>
            </div>
          </div>
        </div>
      )}
    </>
  );
}
