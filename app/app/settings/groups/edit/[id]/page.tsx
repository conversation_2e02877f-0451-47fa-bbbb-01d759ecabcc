"use client";

import { useRouter, useParams } from "next/navigation";
import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { EditGroupForm } from "@/components/settings/groups/edit-group";
import { group } from "@/components/settings/groups/group-management";
import { ArrowLeft } from "lucide-react";
import apiClient from "@/lib/apiClient";

export default function EditGroupPage() {
  const router = useRouter();
  const params = useParams();
  const id = params.id as string;
  const name = JSON.parse(sessionStorage.getItem("name") || "");

  const handleSubmit = async (updatedGroup: group) => {

    const new_group_name = updatedGroup.new_group_name;
    try {
      const response = await apiClient.patch(`/group/${id}`, {
        new_group_name: new_group_name,
      });
      console.log("Group updated:", response.data);
    } catch (error) {
      console.error("Failed to update group:", error);
      alert("Failed to update group");
    } finally {
      router.push("/app/settings/groups");
    }
  };

  const handleCancel = () => {
    sessionStorage.removeItem("name");
    router.push("/app/settings/groups");
  };

  return (
    <div className="p-6 max-w-2xl mx-auto">
      {/* Header with back button */}
      <div className="flex items-center gap-4 mb-6">
        <Button
          variant="ghost"
          size="sm"
          onClick={handleCancel}
          className="flex items-center gap-2"
        >
          <ArrowLeft className="h-4 w-4" />
          Back to Groups
        </Button>
      </div>

      {/* Page title */}
      <div className="mb-8">
        <h1 className="text-3xl font-bold">Edit Group</h1>
        <p className="text-gray-600 mt-2">
          Update group information for <strong>{name}</strong>.
        </p>
      </div>

      {/* Form container */}
      <div className="bg-white p-6 rounded-lg border shadow-sm">
        <EditGroupForm group={name} onSubmit={handleSubmit} />

        {/* Cancel button */}
        <div className="mt-6 pt-4 border-t">
          <Button variant="outline" onClick={handleCancel} className="w-full">
            Cancel
          </Button>
        </div>
      </div>
    </div>
  );
}
