import { Page<PERSON>ontainer } from "@/components/ui/page-container";
import { PageHeader } from "@/components/ui/page-header";
import { StatsCard } from "@/components/ui/stats-card";
import { StatsGrid } from "@/components/ui/stats-grid";
import { Users, Shield, UserCheck, Headphones, TrendingUp } from "lucide-react";

export default function SettingsPage() {
  return (
    <PageContainer>
      <PageHeader
        title="Settings Overview"
        description="Manage your system settings and user permissions"
      />

      <StatsGrid columns={5}>
        <StatsCard
          title="Total Users"
          value="35"
          icon={<Users className="h-6 w-6 text-blue-500" />}
        />

        <StatsCard
          title="Super Admin"
          value="3 / 35"
          description="System administrators"
          icon={<Shield className="h-6 w-6 text-red-500" />}
        />

        <StatsCard
          title="Admin"
          value="5 / 35"
          description="Administrative users"
          icon={<UserCheck className="h-6 w-6 text-orange-500" />}
        />

        <StatsCard
          title="Support"
          value="20 / 35"
          description="Support team members"
          icon={<Headphones className="h-6 w-6 text-green-500" />}
        />

        <StatsCard
          title="Sales"
          value="17 / 35"
          description="Sales team members"
          icon={<TrendingUp className="h-6 w-6 text-purple-500" />}
        />
      </StatsGrid>
    </PageContainer>
  );
}
