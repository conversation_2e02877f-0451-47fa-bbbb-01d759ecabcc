"use client";
import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import {
  OrganizationTable,
  organization,
} from "@/components/settings/organizations/organization-management";
import { Button } from "@/components/ui/button";
import apiClient from "@/lib/apiClient";

export default function OrganizationManagementPage() {
  const router = useRouter();

  const [organizations, setOrganizations] = useState<organization[]>([]);
  const [search, setSearch] = useState("");
  const filteredOrganizations = organizations.filter((organization) =>
    organization.name.toLowerCase().includes(search.toLowerCase())
  );

  const handleDeleteOrganization = async (id: string) => {
    try {
      const response = await apiClient.delete(`/organization/${id}`);
      console.log("Organization deleted:", response.data);
      setOrganizations(
        organizations.filter((organization) => organization.id !== id)
      );
    } catch (error) {
      console.error("Failed to delete organization:", error);
      alert("Failed to delete organization");
    }
  };

  return (
    <div className="p-6 space-y-8">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold">Organization Management Portal</h1>
        <input
          type="text"
          placeholder="Search organizations."
          value={search}
          onChange={(e) => setSearch(e.target.value)}
          className="mb-4 w-[250px] p-2 border rounded-lg focus:outline-none focus:ring focus:border-blue-300"
        />
        <Button
          className="ml-20"
          onClick={() => router.push("/app/settings/organizations/add")}
        >
          Add New Organization
        </Button>
      </div>

      <OrganizationTable
        organizations={filteredOrganizations}
        setOrganizations={setOrganizations}
        onDelete={handleDeleteOrganization}
      />
    </div>
  );
}

export function DeleteOrganizationDialog({
  organization,
  onDelete,
}: {
  organization: organization;
  onDelete: (id: string) => void;
}) {
  const [showConfirm, setShowConfirm] = useState(false);
  const [inputName, setInputName] = useState("");

  const handleDelete = () => {
    if (inputName === organization.name) {
      onDelete(organization.id);
      setShowConfirm(false);
    } else {
      alert("Organization name does not match");
      setInputName("");
    }
  };

  useEffect(() => {
    const handleKeyDown = (e) => {
      if (e.key === "Escape") {
        setShowConfirm(false);
        setInputName("");
      }
    };

    if (showConfirm) {
      document.addEventListener("keydown", handleKeyDown);
    }

    return () => {
      document.removeEventListener("keydown", handleKeyDown);
    };
  }, [showConfirm]);

  return (
    <div>
      <button
        onClick={() => setShowConfirm(true)}
        className="bg-red-600 text-white px-1 py-0 space-x-5 rounded"
      >
        Delete
      </button>
      {showConfirm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center">
          <div className="bg-white p-7 rounded w-auto">
            <h2 className="text-xl font-bold mb-4 text-center">
              Confirm Deletion
            </h2>
            <p className="mb-2 text-left">
              Are you sure you want to delete the organization{" "}
              <strong>{organization.name}</strong>?
            </p>
            <p className="mb-4 text-sm text-gray-600 text-left">
              Type the organization name to confirm:
            </p>
            <input
              className="border px-3 py-2 w-full mb-4"
              value={inputName}
              onChange={(e) => setInputName(e.target.value)}
              placeholder="Enter organization name"
            />
            <div className="flex justify-end gap-2">
              <button
                onClick={() => setShowConfirm(false)}
                className="px-4 py-2 bg-gray-300 rounded"
              >
                Cancel
              </button>
              <button
                onClick={handleDelete}
                className="px-4 py-2 bg-red-600 text-white rounded"
              >
                Confirm Delete
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
