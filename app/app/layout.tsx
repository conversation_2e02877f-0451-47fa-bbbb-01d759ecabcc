// app/app/layout.tsx
import RootLayout, { commonMetadata } from "../root-layout";
import Template from "@/components/template";
import { RouteAuthenticator } from "@/components/Auth/RouteAuth";
import type React from "react";
import type { Metadata } from "next";

// Merge common metadata with any app/dashboard-specific metadata (optional)
export const metadata: Metadata = {
  ...commonMetadata, // Spread common metadata
  title: {
    default: "Dashboard", // Keep specific default for dashboard
    template: "%s | Workalaya",
  },
  // description, icons etc. are inherited from commonMetadata unless overridden
};

export default function AppRootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <RootLayout>
      <RouteAuthenticator>
        <Template>{children}</Template>
      </RouteAuthenticator>
    </RootLayout>
  );
}
