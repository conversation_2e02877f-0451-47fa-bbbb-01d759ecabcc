@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    /* Core colors */
    --background: 0 0% 100%;
    --foreground: 0 0% 3.9%;

    /* Card colors */
    --card: 0 0% 100%;
    --card-foreground: 0 0% 3.9%;

    /* Popover colors */
    --popover: 0 0% 100%;
    --popover-foreground: 0 0% 3.9%;

    /* Primary colors */
    --primary: 0 0% 9%;
    --primary-foreground: 0 0% 98%;

    /* Secondary colors */
    --secondary: 0 0% 96.1%;
    --secondary-foreground: 0 0% 9%;

    /* Muted colors */
    --muted: 0 0% 96.1%;
    --muted-foreground: 0 0% 45.1%;

    /* Accent colors */
    --accent: 0 0% 96.1%;
    --accent-foreground: 0 0% 9%;

    /* Destructive colors */
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;

    /* Border and input colors */
    --border: 0 0% 89.8%;
    --input: 0 0% 89.8%;
    --ring: 0 0% 3.9%;

    /* Chart colors */
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;

    /* Layout */
    --radius: 0.5rem;

    /* Sidebar colors */
    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 240 5.9% 10%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 4.8% 95.9%;
    --sidebar-accent-foreground: 240 5.9% 10%;
    --sidebar-border: 0 0% 89.8%;
    --sidebar-ring: 0 0% 3.9%;
  }

  .dark {
    /* Core colors */
    --background: 0 0% 3.9%;
    --foreground: 0 0% 98%;

    /* Card colors */
    --card: 0 0% 3.9%;
    --card-foreground: 0 0% 98%;

    /* Popover colors */
    --popover: 0 0% 3.9%;
    --popover-foreground: 0 0% 98%;

    /* Primary colors */
    --primary: 0 0% 98%;
    --primary-foreground: 0 0% 9%;

    /* Secondary colors */
    --secondary: 0 0% 14.9%;
    --secondary-foreground: 0 0% 98%;

    /* Muted colors */
    --muted: 0 0% 14.9%;
    --muted-foreground: 0 0% 63.9%;

    /* Accent colors */
    --accent: 0 0% 14.9%;
    --accent-foreground: 0 0% 98%;

    /* Destructive colors */
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;

    /* Border and input colors */
    --border: 0 0% 14.9%;
    --input: 0 0% 14.9%;
    --ring: 0 0% 83.1%;

    /* Chart colors */
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;

    /* Sidebar colors */
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground font-sans;
  }
}

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }
}
