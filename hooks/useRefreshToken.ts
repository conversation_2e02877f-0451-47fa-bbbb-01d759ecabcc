"use client";
import { useEffect, useCallback, useRef } from "react";
import Cookies from "js-cookie";
import apiClient from "@/lib/apiClient";

interface TokenResponse {
  accessToken: string;
  refreshToken?: string;
}

interface TokenManager {
  getTokens: () => { accessToken: string | undefined; refreshToken: string | undefined };
  setTokens: (tokens: TokenResponse) => void;
  clearTokens: () => void;
  refreshToken: () => Promise<boolean>;
}

// Add a global refresh lock to prevent multiple simultaneous refresh attempts
let isRefreshing = false;
let refreshPromise: Promise<boolean> = Promise.resolve(false);

// Token validation utilities
const TokenUtils = {
  decodeToken: (token: string) => {
    try {
      return JSON.parse(atob(token.split(".")[1]));
    } catch (error) {
      return null;
    }
  },

  isTokenValid: (token: string) => {
    const payload = TokenUtils.decodeToken(token);
    if (!payload) return false;

    const expirationTime = payload.exp * 1000;
    const currentTime = Date.now();
    // Consider token valid if it has at least 5 minutes remaining
    return expirationTime - currentTime > 5 * 60 * 1000;
  },

  shouldRefreshToken: (accessToken: string | null) => {
    if (!accessToken) return true;
    
    const payload = TokenUtils.decodeToken(accessToken);
    if (!payload) return true;

    const expirationTime = payload.exp * 1000;
    const currentTime = Date.now();

    // Refresh if access token expires in less than 10 minutes
    return expirationTime - currentTime < 10 * 60 * 1000;
  }
};

export function useRefreshToken(): TokenManager {
  const hasRefreshedOnMount = useRef(false);
  const refreshTimeoutRef = useRef<ReturnType<typeof setTimeout> | null>(null);

  // const calculateRefreshTime = (accessToken: string): number => {
  //   const payload = TokenUtils.decodeToken(accessToken);
  //   if (!payload) return 0;
    
  //   const expirationTime = payload.exp * 1000; // Convert to milliseconds
  //   const currentTime = Date.now();
  //   const timeUntilExpiration = expirationTime - currentTime;
    
  //   // Return time until 2 minutes before expiration
  //   return Math.max(0, timeUntilExpiration - (2 * 60 * 1000));
  // };

  // const scheduleRefresh = (accessToken: string) => {
  //   // Clear any existing timeout
  //   if (refreshTimeoutRef.current) {
  //     clearTimeout(refreshTimeoutRef.current);
  //     refreshTimeoutRef.current = null;
  //   }

  //   const refreshTime = calculateRefreshTime(accessToken);
  //   if (refreshTime <= 0) {
  //     console.log("Token already expired or about to expire, refreshing immediately");
  //     refreshToken(true);
  //     return;
  //   }

  //   console.log(`Scheduling refresh in ${refreshTime / 1000} seconds`);
  //   refreshTimeoutRef.current = setTimeout(() => {
  //     console.log("Scheduled refresh executing");
  //     refreshToken(true);
  //   }, refreshTime);
  // };

  const getTokens = () => {
    const accessToken = Cookies.get("accessToken");
    const refreshToken = Cookies.get("refreshToken");
    console.log("Getting tokens:", { 
      hasAccessToken: !!accessToken, 
      hasRefreshToken: !!refreshToken,
      accessTokenExpiry: accessToken ? new Date(TokenUtils.decodeToken(accessToken)?.exp * 1000) : null,
      refreshTokenExpiry: refreshToken ? new Date(TokenUtils.decodeToken(refreshToken)?.exp * 1000) : null
    });
    return { accessToken, refreshToken };
  };

  const setTokens = ({ accessToken, refreshToken }: TokenResponse) => {
    console.log("Setting tokens:", { 
      hasAccessToken: !!accessToken,
      hasRefreshToken: !!refreshToken,
      accessTokenExpiry: accessToken ? new Date(TokenUtils.decodeToken(accessToken)?.exp * 1000) : null,
      refreshTokenExpiry: refreshToken ? new Date(TokenUtils.decodeToken(refreshToken)?.exp * 1000) : null
    });

    if (accessToken) {
      Cookies.set("accessToken", accessToken, {
        expires: new Date(Date.now() + 30 * 60 * 1000), // 30 minutes
        path: "/",
        sameSite: "Strict",
        secure: window.location.protocol === "https:"
      });
      
      // // Schedule refresh when new token is set
      // scheduleRefresh(accessToken);
    }

    if (refreshToken) {
      Cookies.set("refreshToken", refreshToken, {
        expires: new Date(Date.now() + 15 * 24 * 60 * 60 * 1000), // 15 days
        path: "/",
        sameSite: "Strict",
        secure: window.location.protocol === "https:"
      });
    }

    // Verify tokens were set
    const verifyTokens = getTokens();
    console.log("Verified tokens after setting:", {
      hasAccessToken: !!verifyTokens.accessToken,
      hasRefreshToken: !!verifyTokens.refreshToken,
      accessTokenExpiry: verifyTokens.accessToken ? new Date(TokenUtils.decodeToken(verifyTokens.accessToken)?.exp * 1000) : null,
      refreshTokenExpiry: verifyTokens.refreshToken ? new Date(TokenUtils.decodeToken(verifyTokens.refreshToken)?.exp * 1000) : null
    });
  };

  const clearTokens = () => {
    console.log("Clearing tokens");
    Cookies.remove("accessToken", { path: "/" });
    Cookies.remove("refreshToken", { path: "/" });
    
    // Clear any existing refresh timeout
    if (refreshTimeoutRef.current) {
      clearTimeout(refreshTimeoutRef.current);
      refreshTimeoutRef.current = null;
    }
    
    // Verify tokens were cleared
    const verifyTokens = getTokens();
    console.log("Verified tokens after clearing:", {
      hasAccessToken: !!verifyTokens.accessToken,
      hasRefreshToken: !!verifyTokens.refreshToken
    });
  };

  const refreshToken = async (force = false): Promise<boolean> => {
    console.log("Attempting token refresh, force:", force);

    const { accessToken, refreshToken: currentRefreshToken } = getTokens();
    
    // If we have a valid access token and refresh isn't forced, don't refresh
    if (!force && accessToken && TokenUtils.isTokenValid(accessToken)) {
      console.log("Access token is still valid, skipping refresh");
      return true;
    }

    // If we're already refreshing, wait for that to complete
    if (isRefreshing) {
      console.log("Refresh already in progress, waiting...");
      return refreshPromise;
    }

    // If we don't have a refresh token, we can't refresh
    if (!currentRefreshToken) {
      console.log("No refresh token available");
      return false;
    }

    isRefreshing = true;

    try {
      console.log("Making refresh token request...");
      
      refreshPromise = apiClient.post<TokenResponse>("/token", {
        refreshToken: currentRefreshToken,
      })
        .then(({ data }) => {
          console.log("Refresh response received:", {
            hasAccessToken: !!data.accessToken
          });

          if (data.accessToken) {
            setTokens({ 
              accessToken: data.accessToken,
              refreshToken: currentRefreshToken 
            });
            return true;
          } else {
            console.log("No access token in response, keeping existing tokens");
            return false;
          }
        })
        .catch((error) => {
          console.error("Token refresh error:", error);
          
          // Retry after 1 second if we have a valid refresh token
          if (currentRefreshToken && TokenUtils.isTokenValid(currentRefreshToken)) {
            console.log("Retrying refresh after 1 second...");
            setTimeout(() => {
              refreshToken(true);
            }, 1000);
          } else if (!accessToken || !TokenUtils.isTokenValid(accessToken)) {
            console.log("Clearing tokens due to refresh failure and no valid access token");
            clearTokens();
          } else {
            console.log("Keeping existing valid access token despite refresh failure");
          }
          return false;
        })
        .finally(() => {
          isRefreshing = false;
          refreshPromise = Promise.resolve(false);
        });

      return refreshPromise;
    } catch (error) {
      console.error("Unexpected error during refresh:", error);
      isRefreshing = false;
      refreshPromise = Promise.resolve(false);
      if (!accessToken || !TokenUtils.isTokenValid(accessToken)) {
        clearTokens();
      }
      return false;
    }
  };

  // Add useEffect for initial token refresh and cleanup
  useEffect(() => {
    const tokens = getTokens();
    console.log("useRefreshToken mounted, tokens:", { 
      hasAccessToken: !!tokens.accessToken, 
      hasRefreshToken: !!tokens.refreshToken,
      hasRefreshedOnMount: hasRefreshedOnMount.current
    });

    if (!tokens.refreshToken) return;

    // Only refresh on mount if we haven't already
    if (!hasRefreshedOnMount.current) {
      hasRefreshedOnMount.current = true;
      refreshToken(true);
    } else if (tokens.accessToken) {
      // // If we have an access token, schedule its refresh
      // scheduleRefresh(tokens.accessToken);
    }

    return () => {
      // Clear any existing refresh timeout on unmount
      if (refreshTimeoutRef.current) {
        clearTimeout(refreshTimeoutRef.current);
        refreshTimeoutRef.current = null;
      }
      hasRefreshedOnMount.current = false;
    };
  }, [refreshToken]);

  return {
    getTokens,
    setTokens,
    clearTokens,
    refreshToken: () => refreshToken(true) // Always force refresh when called directly
  };
} 