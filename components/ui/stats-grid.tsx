import * as React from "react";
import { cn } from "@/lib/utils";

interface StatsGridProps extends React.HTMLAttributes<HTMLDivElement> {
  children: React.ReactNode;
  columns?: 2 | 3 | 4 | 5;
}

const StatsGrid = React.forwardRef<HTMLDivElement, StatsGridProps>(
  ({ className, children, columns = 4, ...props }, ref) => {
    const gridCols = {
      2: "grid-cols-1 sm:grid-cols-2",
      3: "grid-cols-1 sm:grid-cols-2 lg:grid-cols-3",
      4: "grid-cols-1 sm:grid-cols-2 lg:grid-cols-4",
      5: "grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5",
    };

    return (
      <div
        ref={ref}
        className={cn("grid gap-4", gridCols[columns], className)}
        {...props}
      >
        {children}
      </div>
    );
  }
);
StatsGrid.displayName = "StatsGrid";

export { StatsGrid };
