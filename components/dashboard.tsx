"use client";

export const metadata = {
  // This metadata will be ignored because of "use client". Remove it or move to layout.tsx.
  title: "Workalaya",
  description: "Workalaya",
};

import { useState } from "react";
import { useRouter } from "next/navigation"; // Correct import for App Router
import { customers, CustomerType } from "@/app/data/customers";

import {
  LayoutGrid,
  Users,
  Ticket,
  BarChart3,
  Bell,
  Activity,
  TrendingUp,
  DollarSign,
  MinusCircle,
  XCircle,
  UsersRound,
} from "lucide-react";

type CustomerFilter = "All" | "Active" | "Inactive" | "Expired";

export default function DashboardPage() {
  const [currentFilter, setCurrentFilter] = useState<CustomerFilter>("All");
  const router = useRouter(); // Initialize useRouter

  const totalCustomers = customers.length;
  const activeCustomers = customers.filter(
    (customer: CustomerType) => customer.status === "Active"
  ).length;
  const inactiveCustomers = customers.filter(
    (customer: CustomerType) => customer.status === "Inactive"
  ).length;
  const expiredCustomers = customers.filter(
    (customer: CustomerType) => customer.status === "Expired"
  ).length;

  const totalRevenue = "NPR 45,000"; // Placeholder
  const totalInvoices = "150"; // Placeholder
  const pendingInvoices = "20"; // Placeholder
  const openTickets = "25"; // Placeholder

  const handleFilterClick = (filter: CustomerFilter) => {
    setCurrentFilter(filter);
    // Navigate to the /customers page, passing the filter as a query parameter
    router.push(`/app/customers?filter=${filter}`);
  };
  return (
    <div className="flex flex-1 overflow-hidden">
      {/* Dashboard Content */}
      <main className=" flex-1  overflow-auto">
        {/* Client Stats - Top Row with clear status colors */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
          {/* Total Customers Box */}
          <div
            className={`bg-white text-blue-700 p-4 rounded-lg shadow-md border 
              ${
                currentFilter === "All"
                  ? "border-blue-500 ring-2 ring-blue-200"
                  : "border-gray-200 hover:border-blue-300"
              }
              text-center cursor-pointer transition-all duration-200 flex items-center justify-between`}
            onClick={() => handleFilterClick("All")}
          >
            <div>
              <h3 className="text-sm font-medium">Total Customers</h3>
              <p className="text-2xl font-bold">{totalCustomers}</p>
            </div>
            <UsersRound className="h-8 w-8 text-blue-500" />{" "}
            {/* Icon colored to match border */}
          </div>

          {/* Active Clients Box */}
          <div
            className={`bg-white text-green-700 p-4 rounded-lg shadow-md border 
              ${
                currentFilter === "Active"
                  ? "border-green-500 ring-2 ring-green-200"
                  : "border-gray-200 hover:border-green-300"
              }
              text-center cursor-pointer transition-all duration-200 flex items-center justify-between`}
            onClick={() => handleFilterClick("Active")}
          >
            <div>
              <h3 className="text-sm font-medium">Active Clients</h3>
              <p className="text-2xl font-bold">{activeCustomers}</p>
            </div>
            <Activity className="h-8 w-8 text-green-500" />{" "}
            {/* Icon colored to match border */}
          </div>

          {/* Inactive Clients Box */}
          <div
            className={`bg-white text-orange-700 p-4 rounded-lg shadow-md border 
              ${
                currentFilter === "Inactive"
                  ? "border-orange-500 ring-2 ring-orange-200"
                  : "border-gray-200 hover:border-orange-300"
              }
              text-center cursor-pointer transition-all duration-200 flex items-center justify-between`}
            onClick={() => handleFilterClick("Inactive")}
          >
            <div>
              <h3 className="text-sm font-medium">Inactive Clients</h3>
              <p className="text-2xl font-bold">{inactiveCustomers}</p>
            </div>
            <MinusCircle className="h-8 w-8 text-orange-500" />{" "}
            {/* Icon colored to match border */}
          </div>

          {/* Expired Clients Box */}
          <div
            className={`bg-white text-red-700 p-4 rounded-lg shadow-md border 
              ${
                currentFilter === "Expired"
                  ? "border-red-500 ring-2 ring-red-200"
                  : "border-gray-200 hover:border-red-300"
              }
              text-center cursor-pointer transition-all duration-200 flex items-center justify-between`}
            onClick={() => handleFilterClick("Expired")}
          >
            <div>
              <h3 className="text-sm font-medium">Expired Clients</h3>
              <p className="text-2xl font-bold">{expiredCustomers}</p>
            </div>
            <XCircle className="h-8 w-8 text-red-500" />{" "}
            {/* Icon colored to match border */}
          </div>
        </div>

        {/* Metric Cards - Updated to use calculated values */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
          <div className="bg-white p-4 rounded shadow flex flex-col items-center justify-center">
            <DollarSign className="text-green-500 h-8 w-8 mb-2" />
            <div className="text-xl font-bold">{totalRevenue}</div>
            <div className="text-sm text-gray-500">Monthly Revenue</div>
          </div>
          <div className="bg-white p-4 rounded shadow flex flex-col items-center justify-center  hover:border-blue-300" onClick={() => handleFilterClick("Inactive")}>
            
            <Ticket className="text-blue-500 h-8 w-8 mb-2" />
            <div className="text-xl font-bold">{totalInvoices}</div>
            <div className="text-sm text-gray-500">Total Invoices</div>
          </div>
          <div className="bg-white p-4 rounded shadow flex flex-col items-center justify-center">
            <Activity className="text-yellow-500 h-8 w-8 mb-2" />
            <div className="text-xl font-bold">{pendingInvoices}</div>
            <div className="text-sm text-gray-500">Pending Invoices</div>
          </div>
          <div className="bg-white p-4 rounded shadow flex flex-col items-center justify-center">
            <TrendingUp className="text-red-500 h-8 w-8 mb-2" />
            <div className="text-xl font-bold">{openTickets}</div>
            <div className="text-sm text-gray-500">Open Tickets</div>
          </div>
        </div>

        {/* Icons and Values Row */}
        {/* <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
          <div className="bg-white p-4 rounded shadow flex items-center gap-4">
            <Users className="text-[#4a90c7] h-6 w-6" />
            <div>
              <div className="text-xl font-bold">{totalCustomers}</div>
              <div className="text-sm text-gray-500">Total Clients</div>
            </div>
          </div>
          <div className="bg-white p-4 rounded shadow flex items-center gap-4">
            <Bell className="text-green-500 h-6 w-6" />
            <div>
              <div className="text-xl font-bold">{activeCustomers}</div>
              <div className="text-sm text-gray-500">Active Clients</div>
            </div>
          </div>
          <div className="bg-white p-4 rounded shadow flex items-center gap-4">
            <BarChart3 className="text-yellow-500 h-6 w-6" />
            <div>
              <div className="text-xl font-bold">{totalRevenue}</div>
              <div className="text-sm text-gray-500">Monthly Revenue</div>
            </div>
          </div>
          <div className="bg-white p-4 rounded shadow flex items-center gap-4">
            <LayoutGrid className="text-red-500 h-6 w-6" />
            <div>
              <div className="text-xl font-bold">{openTickets}</div>
              <div className="text-sm text-gray-500">Open Tickets</div>
            </div>
          </div>
        </div> */}

        {/* Charts Section */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
          <div className="bg-[#3a5f8f] text-white p-4 rounded h-[180px] flex items-center justify-center">
            <span className="text-sm">Revenue Chart</span>
          </div>
          <div className="bg-[#3a5f8f] text-white p-4 rounded h-[180px] flex items-center justify-center">
            <span className="text-sm">Client Activity</span>
          </div>
          <div className="md:col-span-2 bg-[#3a5f8f] text-white p-4 rounded h-[180px] flex items-center justify-center">
            <span className="text-sm">Customer Demographics Chart</span>
          </div>
        </div>

        {/* Main Panels: Recent Activities & Revenue Chart */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-4 mb-6">
          {/* Recent Activities */}
          <div className="bg-white p-4 rounded shadow col-span-1 lg:col-span-2">
            <div className="font-bold mb-2">Recent Activities</div>
            <ul className="space-y-2">
              <li className="border-b pb-2 text-sm">
                John Doe signed up (2 hrs ago)
              </li>
              <li className="border-b pb-2 text-sm">
                Invoice #1234 paid (5 hrs ago)
              </li>
              <li className="border-b pb-2 text-sm">
                Ticket #456 updated (1 day ago)
              </li>
              <li className="text-sm">New report generated (2 days ago)</li>
            </ul>
          </div>

          {/* Placeholder Chart Panel */}
          <div className="bg-white p-4 rounded shadow col-span-1">
            <div className="font-bold mb-2">Revenue Chart</div>
            <div className="h-[180px] bg-gray-200 rounded flex items-center justify-center text-gray-500">
              Chart Placeholder
            </div>
          </div>
        </div>

        {/* Table Section */}
        <div className="bg-white p-4 rounded shadow overflow-x-auto">
          <div className="font-bold mb-2">Client List</div>
          <table className="min-w-full text-sm">
            <thead>
              <tr className="text-left border-b">
                <th className="py-2 px-2">Name</th>
                <th className="py-2 px-2">Email</th>
                <th className="py-2 px-2">Status</th>
              </tr>
            </thead>
            <tbody>
              <tr className="border-b">
                <td className="py-2 px-2">John Doe</td>
                <td className="py-2 px-2"><EMAIL></td>
                <td className="py-2 px-2 text-green-600">Active</td>
              </tr>
              <tr className="border-b">
                <td className="py-2 px-2">Jane Smith</td>
                <td className="py-2 px-2"><EMAIL></td>
                <td className="py-2 px-2 text-yellow-500">Pending</td>
              </tr>
              <tr>
                <td className="py-2 px-2">Bob Johnson</td>
                <td className="py-2 px-2"><EMAIL></td>
                <td className="py-2 px-2 text-red-500">Inactive</td>
              </tr>
            </tbody>
          </table>
        </div>
      </main>
    </div>
  );
}
