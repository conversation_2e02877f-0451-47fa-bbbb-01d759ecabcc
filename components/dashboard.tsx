"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { customers, CustomerType } from "@/app/data/customers";
import { PageContainer } from "@/components/ui/page-container";
import { PageHeader } from "@/components/ui/page-header";
import { StatsCard } from "@/components/ui/stats-card";
import { StatsGrid } from "@/components/ui/stats-grid";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";

import {
  LayoutGrid,
  Users,
  Ticket,
  BarChart3,
  Bell,
  Activity,
  TrendingUp,
  DollarSign,
  MinusCircle,
  XCircle,
  UsersRound,
} from "lucide-react";

type CustomerFilter = "All" | "Active" | "Inactive" | "Expired";

export default function DashboardPage() {
  const [currentFilter, setCurrentFilter] = useState<CustomerFilter>("All");
  const router = useRouter(); // Initialize useRouter

  const totalCustomers = customers.length;
  const activeCustomers = customers.filter(
    (customer: CustomerType) => customer.status === "Active"
  ).length;
  const inactiveCustomers = customers.filter(
    (customer: CustomerType) => customer.status === "Inactive"
  ).length;
  const expiredCustomers = customers.filter(
    (customer: CustomerType) => customer.status === "Expired"
  ).length;

  const totalRevenue = "NPR 45,000"; // Placeholder
  const totalInvoices = "150"; // Placeholder
  const pendingInvoices = "20"; // Placeholder
  const openTickets = "25"; // Placeholder

  const handleFilterClick = (filter: CustomerFilter) => {
    setCurrentFilter(filter);
    // Navigate to the /customers page, passing the filter as a query parameter
    router.push(`/app/customers?filter=${filter}`);
  };
  return (
    <PageContainer>
      <PageHeader
        title="Dashboard"
        description="Overview of your business metrics and customer data"
      />

      {/* Customer Stats */}
      <StatsGrid columns={4}>
        <StatsCard
          title="Total Customers"
          value={totalCustomers}
          icon={<UsersRound className="h-6 w-6 text-blue-500" />}
          isActive={currentFilter === "All"}
          onClick={() => handleFilterClick("All")}
        />

        <StatsCard
          title="Active Clients"
          value={activeCustomers}
          icon={<Activity className="h-6 w-6 text-green-500" />}
          isActive={currentFilter === "Active"}
          onClick={() => handleFilterClick("Active")}
        />

        <StatsCard
          title="Inactive Clients"
          value={inactiveCustomers}
          icon={<MinusCircle className="h-6 w-6 text-orange-500" />}
          isActive={currentFilter === "Inactive"}
          onClick={() => handleFilterClick("Inactive")}
        />

        <StatsCard
          title="Expired Clients"
          value={expiredCustomers}
          icon={<XCircle className="h-6 w-6 text-red-500" />}
          isActive={currentFilter === "Expired"}
          onClick={() => handleFilterClick("Expired")}
        />
      </StatsGrid>

      {/* Business Metrics */}
      <StatsGrid columns={4}>
        <StatsCard
          title="Monthly Revenue"
          value={`$${totalRevenue}`}
          icon={<DollarSign className="h-6 w-6 text-green-500" />}
          trend={{ value: 12, label: "vs last month", isPositive: true }}
        />

        <StatsCard
          title="Total Invoices"
          value={totalInvoices}
          icon={<Ticket className="h-6 w-6 text-blue-500" />}
        />

        <StatsCard
          title="Pending Invoices"
          value={pendingInvoices}
          icon={<Activity className="h-6 w-6 text-yellow-500" />}
        />

        <StatsCard
          title="Open Tickets"
          value={openTickets}
          icon={<TrendingUp className="h-6 w-6 text-red-500" />}
        />
      </StatsGrid>

      {/* Charts Section */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card className="h-[300px]">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <BarChart3 className="h-5 w-5" />
              Revenue Chart
            </CardTitle>
          </CardHeader>
          <CardContent className="flex items-center justify-center h-full">
            <div className="text-muted-foreground">Chart placeholder</div>
          </CardContent>
        </Card>

        <Card className="h-[300px]">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Activity className="h-5 w-5" />
              Client Activity
            </CardTitle>
          </CardHeader>
          <CardContent className="flex items-center justify-center h-full">
            <div className="text-muted-foreground">Chart placeholder</div>
          </CardContent>
        </Card>

        <Card className="md:col-span-2 h-[300px]">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Users className="h-5 w-5" />
              Customer Demographics
            </CardTitle>
          </CardHeader>
          <CardContent className="flex items-center justify-center h-full">
            <div className="text-muted-foreground">Chart placeholder</div>
          </CardContent>
        </Card>
      </div>

      {/* Recent Activities */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <Card className="lg:col-span-2">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Bell className="h-5 w-5" />
              Recent Activities
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center gap-3 p-3 rounded-lg bg-muted/50">
                <div className="h-2 w-2 bg-green-500 rounded-full"></div>
                <div className="flex-1">
                  <p className="text-sm font-medium">John Doe signed up</p>
                  <p className="text-xs text-muted-foreground">2 hours ago</p>
                </div>
              </div>
              <div className="flex items-center gap-3 p-3 rounded-lg bg-muted/50">
                <div className="h-2 w-2 bg-blue-500 rounded-full"></div>
                <div className="flex-1">
                  <p className="text-sm font-medium">Invoice #1234 paid</p>
                  <p className="text-xs text-muted-foreground">5 hours ago</p>
                </div>
              </div>
              <div className="flex items-center gap-3 p-3 rounded-lg bg-muted/50">
                <div className="h-2 w-2 bg-yellow-500 rounded-full"></div>
                <div className="flex-1">
                  <p className="text-sm font-medium">Ticket #456 updated</p>
                  <p className="text-xs text-muted-foreground">1 day ago</p>
                </div>
              </div>
              <div className="flex items-center gap-3 p-3 rounded-lg bg-muted/50">
                <div className="h-2 w-2 bg-purple-500 rounded-full"></div>
                <div className="flex-1">
                  <p className="text-sm font-medium">New report generated</p>
                  <p className="text-xs text-muted-foreground">2 days ago</p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <TrendingUp className="h-5 w-5" />
              Quick Stats
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex justify-between items-center">
                <span className="text-sm text-muted-foreground">
                  Growth Rate
                </span>
                <span className="text-sm font-medium text-green-600">+12%</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm text-muted-foreground">
                  Conversion
                </span>
                <span className="text-sm font-medium">3.2%</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm text-muted-foreground">
                  Avg. Revenue
                </span>
                <span className="text-sm font-medium">$2,340</span>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </PageContainer>
  );
}
