"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import  ISPpackagesForm from "@/components/packages/isp-packages-form"

export default function PackageDetails() {
  const [showForm, setShowForm] = useState(false)

  // Sample packages list (Replace with API data later)
  // const packages = [
  //   {
  //     id: 1,
  //     packagename: "100/100 Mbps",
  //     up_speed: "100Mbps",
  //     down_speed: "100Mbps",
  //     description: "100Mbps package with 50Gbps FUP on a weekly basis",
  //     fupdetails: "50Gbps",
  //   },
  //   {
  //     id: 2,
  //     packagename: "200/200 Mbps",
  //     up_speed: "200Mbps",
  //     down_speed: "200Mbps",
  //     description: "200Mbps package with 100Gbps FUP on a weekly basis",
  //     fupdetails: "100Gbps",
  //   },
  //   {
  //     id: 3,
  //     packagename: "300/300 Mbps",
  //     up_speed: "300Mbps",
  //     down_speed: "300Mbps",
  //     description: "300Mbps package with 50Gbps FUP on a daily basis",
  //     fupdetails: "50Gbps",
  //   },
  // ]

  return (
    <div className="p-5  mx-auto bg-white rounded-lg">
      <div className="flex justify-between items-center  mb-6">
        <h1 className="text-xl font-bold">Package Details</h1>
        <Button size="sm" onClick={() => setShowForm(!showForm)}>
          {showForm ? "Cancel" : "Add Package"}
        </Button>
      </div>

      {showForm ? (
        <div className="mb-6">
          <ISPpackagesForm />
        </div>
      ) : (
        <div className="overflow-x-auto rounded">
          <table className="min-w-full border text-[13px]">
            <thead className="bg-gray-100 text-left">
              <tr>
                <th className="border px-4 py-2">SN</th>
                <th className="border px-4 py-2">Package Name</th>
                <th className="border px-4 py-2">Upload Speed</th>
                <th className="border px-4 py-2">Download Speed</th>
                <th className="border px-4 py-2">Description</th>
                <th className="border px-4 py-2">F0P Details</th>
                <th className="border px-4 py-2">Action</th>
              </tr>
            </thead>
            <tbody>
              {/* {packages.map((packages, index) => (
                <tr key={packages.id} className="hover:bg-gray-50">
                  <td className="border px-4 py-2">{index + 1}</td>
                  <td className="border px-4 py-2">{packages.packagename}</td>
                  <td className="border px-4 py-2">{packages.up_speed}</td>
                  <td className="border px-4 py-2">{packages.down_speed}</td>
                  <td className="border px-4 py-2">{packages.description}</td>
                  <td className="border px-4 py-2">{packages.fupdetails}</td>
                  <td className="border px-4 py-2 w-1/10">
                    <Button variant="outline" size="ss">Edit</Button>
                    <Button variant="outline" size="ss">Delete</Button>
                  </td>
                </tr>
              ))} */}
            </tbody>
          </table>
        </div>
      )}
    </div>
  )
}