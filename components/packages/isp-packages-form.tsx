"use client";

import { useState } from "react";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import { Check as CheckIcon } from "lucide-react";

interface PackagesAddFormProps {
  onSubmit?: (packageData: any) => void;
  onCancel?: () => void;
  initialData?: any;
  isEdit?: boolean;
}

export default function packagesAddForm({
  onSubmit,
  onCancel,
  initialData,
  isEdit = false,
}: PackagesAddFormProps) {
  const [form, setForm] = useState({
    packagename: initialData?.packagename || "",
    up_speed: initialData?.up_speed || "",
    down_speed: initialData?.down_speed || "",
    price: initialData?.price || "",
    description: initialData?.description || "",
    fupdetails: initialData?.fupdetails || "",
    fupduration: initialData?.fupduration || "",
  });
  const [isOpen, setIsOpen] = useState(false);

  // Form validation - check if all required fields are filled
  const isFormValid = () => {
    return (
      form.packagename.trim() !== "" &&
      form.up_speed.trim() !== "" &&
      form.down_speed.trim() !== "" &&
      form.price.trim() !== "" &&
      form.description.trim() !== ""
    );
  };

  // Update state based on the <details> element's 'open' property
  const handleToggle = (event) => {
    setIsOpen(event.target.open);
    console.log(isOpen);
  };

  const handleChange = (field: keyof typeof form, value: string) => {
    setForm({ ...form, [field]: value });
  };

  const handleSubmit = () => {
    console.log("Submitted:", form);
    if (onSubmit) {
      onSubmit(form);
    }
    // Reset form after submission
    setForm({
      packagename: "",
      up_speed: "",
      down_speed: "",
      price: "",
      description: "",
      fupdetails: "",
      fupduration: "",
    });
  };

  return (
    <div className="bg-white p-6 rounded shadow  mx-auto space-y-6">
      <h2 className="text-xl font-bold">
        {isEdit ? "Edit Package" : "Add Package"}
      </h2>

      {/* Dropdown Fields */}
      <div className="text-xs grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <Label>Package Name *</Label>
          <Input
            type="text"
            value={form.packagename}
            onChange={(e) => handleChange("packagename", e.target.value)}
            placeholder="e.g., 100/100 Mbps"
          />
        </div>

        <div>
          <div>
            <Label>Upload Speed *</Label>
            <Input
              type="text"
              value={form.up_speed}
              onChange={(e) => handleChange("up_speed", e.target.value)}
              placeholder="e.g., 100Mbps"
            />
          </div>
        </div>

        <div>
          <Label>Download Speed *</Label>
          <Input
            type="text"
            value={form.down_speed}
            onChange={(e) => handleChange("down_speed", e.target.value)}
            placeholder="e.g., 100Mbps"
          />
        </div>

        <div>
          <Label>Price *</Label>
          <Input
            type="text"
            value={form.price}
            onChange={(e) => handleChange("price", e.target.value)}
            placeholder="e.g., NPR 2,500"
          />
        </div>

        <div>
          <Label>Description *</Label>
          <Input
            type="text"
            value={form.description}
            onChange={(e) => handleChange("description", e.target.value)}
            placeholder="Package description"
          />
        </div>

        <div>
          <Label>Package Registered Date</Label>
          <Input
            type="date"
            value={form.registerDate}
            onChange={(e) => handleChange("registerDate", e.target.value)}
          />
        </div>

        {/* <div>
          <Label>Apply FUP to this package?</Label>
          <details open={isOpen} onToggle={handleToggle}>
            <summary className="cursor-pointer p-2 bg-gray-100 rounded">
              {isOpen ? 'Hide Options' : 'Show Options'}
            </summary> */}

        {/* Options displayed when open */}
        {/* {isOpen && (
              <div className="mt-2 p-2 border rounded">
                <button className="block w-full p-2 hover:bg-blue-500" onclick="yesfupoption ()">
                  YES
                </button>
                <button className="block w-full p-2 hover:bg-blue-500">
                  NO
                </button>
              </div>
            )}
          </details> */}

        {/* </div> */}

        {/* <div>
          <Label>Apply FUP to this package?</Label>
          <select
            className="mt-1 w-full border rounded px-2 py-2"
            value={form.fupdetails}
            onChange={(e) => handleChange("fupdetails", e.target.value)}
          >
            <option value="">Select</option>
            <option value="yes">YES</option>
            <option value="no">NO</option>
          </select>
        </div> */}

        {/* <div className="space-y-4">
          <Label>Apply FUP to this package?</Label>
          <div className="flex gap-4">
            <button
              type="button"
              className={`w-12 h-12 border-2 rounded-lg flex flex-col items-center justify-center
                ${form.fupdetails === 'yes' ? 'border-blue-500 bg-blue-50' : 'border-gray-300'}
                hover:border-blue-400 transition-colors`}
              onClick={() => handleChange("fupdetails", form.fupdetails === 'yes' ? '' : 'yes')}
            >
              {form.fupdetails === 'yes' && (
                <CheckIcon className="w-6 h-6 text-blue-500 mb-2" />
              )}
              <span className="font-medium">YES</span>
            </button>

            <button
              type="button"
              className={`w-12 h-12 border-2 rounded-lg flex flex-col items-center justify-center
                ${form.fupdetails === 'no' ? 'border-blue-500 bg-blue-50' : 'border-gray-300'}
                hover:border-blue-400 transition-colors`}
              onClick={() => handleChange("fupdetails", form.fupdetails === 'no' ? '' : 'no')}
            >
              {form.fupdetails === 'no' && (
                <CheckIcon className="w-6 h-6 text-blue-500 mb-2" />
              )}
              <span className="font-medium">NO</span>
            </button>
          </div>

          {form.fupdetails === 'yes' && (
            <div className="space-y-4 mt-4">
              <div>
                <Label>FUP Speed</Label>
                <input
                  type="number"
                  className="mt-1 w-full border rounded px-3 py-2"
                  value={form.fupSpeed}
                  onChange={(e) => handleChange("fupSpeed", e.target.value)}
                />
              </div>
              <div>
                <Label>FUP Volume</Label>
                <input
                  type="number"
                  className="mt-1 w-full border rounded px-3 py-2"
                  value={form.fupVolume}
                  onChange={(e) => handleChange("fupVolume", e.target.value)}
                />
              </div>
            </div>
          )}
        </div> */}
        <div className="space-y-4">
          <Label>Apply FUP to this package?</Label>
          <div className="flex gap-4">
            {/* YES Button */}
            <button
              type="button"
              className={`w-10 h-6 border-2 rounded-lg flex items-center justify-center
                ${
                  form.fupdetails === "yes"
                    ? "border-blue-600 bg-blue-600 text-white"
                    : "border-gray-300 bg-white text-black-700"
                }
                hover:border-blue-400 transition-colors font-medium`}
              onClick={() =>
                handleChange(
                  "fupdetails",
                  form.fupdetails === "yes" ? "" : "yes"
                )
              }
            >
              YES
            </button>

            {/* NO Button */}
            <button
              type="button"
              className={`w-10 h-6 border-2 rounded-lg flex items-center justify-center
                ${
                  form.fupdetails === "no"
                    ? "border-blue-600 bg-blue-600 text-white"
                    : "border-gray-300 bg-white text-black-700"
                }
                hover:border-blue-400 transition-colors font-medium`}
              onClick={() =>
                handleChange("fupdetails", form.fupdetails === "no" ? "" : "no")
              }
            >
              NO
            </button>
          </div>

          {/* Show additional options only when YES is selected */}
          {form.fupdetails === "yes" && (
            <div className="mt-4 space-y-4">
              <div>
                <Label>FUP Limit (GB)</Label>
                <input
                  type="number"
                  className="mt-1 w-full border rounded px-3 py-2"
                  value={form.fupLimit}
                  onChange={(e) => handleChange("fupLimit", e.target.value)}
                />
              </div>
              <div>
                <Label>Post-FUP Speed (Mbps)</Label>
                <input
                  type="number"
                  className="mt-1 w-full border rounded px-3 py-2"
                  value={form.postFUPSpeed}
                  onChange={(e) => handleChange("postFUPSpeed", e.target.value)}
                />
              </div>
              <div>
                <Label>Apply FUP on</Label>
                <select
                  className="mt-1 w-full border rounded px-2 py-2"
                  value={form.fupduration}
                  onChange={(e) => handleChange("fupduration", e.target.value)}
                >
                  <option value="">Select</option>
                  <option value="daily">Daily</option>
                  <option value="weekly">Weekly</option>
                  <option value="fortnightly">Fortnightly</option>
                  <option value="Monthly">Monthly</option>
                </select>
              </div>
            </div>
          )}
        </div>
      </div>

      <div className="flex gap-4 pt-4">
        <Button
          onClick={handleSubmit}
          className="bg-blue-600 hover:bg-blue-700"
          disabled={!isFormValid()}
        >
          {isEdit ? "Update Package" : "Submit Package"}
        </Button>
        {onCancel && (
          <Button onClick={onCancel} variant="outline">
            Cancel
          </Button>
        )}
      </div>
    </div>
  );
}
