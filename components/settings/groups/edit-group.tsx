"use client";

import { React, useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { group } from "./group-management";

export interface EditGroupFormProps {
  group: group;
  onSubmit: (group: group) => void;
}

export function EditGroupForm({ group, onSubmit }: EditGroupFormProps) {
  const [new_group_name, setNewGroupName] = useState("");

  const isValid = new_group_name.trim() !== "";

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (isValid) {
      onSubmit({
        ...group,
        name: new_group_name.trim()
      });
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div className="space-y-2">
        <label className="text-sm font-medium text-gray-700">Group Name</label>
        <Input
          value={new_group_name}
          onChange={(e) => setNewGroupName(e.target.value)}
          placeholder="Enter the Group Name"
        />
      </div>
      <Button type="submit" className="w-full" disabled={!isValid}>
        Save Changes
      </Button>
    </form>
  );
}
