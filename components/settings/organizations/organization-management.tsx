// components/organization-management.tsx
"use client";
import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import Cookies from "js-cookie";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import apiClient from "@/lib/apiClient";
import { DeleteOrganizationDialog } from "@/app/app/settings/organizations/page";

export interface organization {
  id: string;
  name: string;
  parent_id: string;
  type: string;
  new_organization_name?: string;
  balance: number;
}

// organization Table Component
export function OrganizationTable({
  onDelete,
  organizations,
  setOrganizations
}: {
  onDelete: (name: string) => void;
  organizations: organization[];
  setOrganizations: (organizations: organization[]) => void;
}) {

  // const [organizations, setOrganizations] = useState<organization[]>([]);

  useEffect(() => {
    const fetchOrganizations = async () => {
      try {
        const response = await apiClient.get("/organization");
        setOrganizations(response.data.data);
      } catch (error) {
      }
    };

    fetchOrganizations();
  }, []);

  return (
    <div className="w-full max-w-4xl mx-auto p-4">
      <div className="rounded-2xl shadow-lg bg-white dark:bg-gray-900 overflow-hidden">
        <Table className="w-full">
          <TableHeader className="bg-gray-100 dark:bg-gray-800">
            <TableRow>
              <TableHead className="text-left text-gray-700 dark:text-gray-300 font-semibold text-lg px-3 py-4">
                S.N.
              </TableHead>
              <TableHead className="text-left text-gray-700 dark:text-gray-300 font-semibold text-lg px-3 py-4">
                Organization Name
              </TableHead>
              <TableHead className="text-left text-gray-700 dark:text-gray-300 font-semibold text-lg px-3 py-4">
                Balance
              </TableHead>
              <TableHead className="text-right text-gray-700 dark:text-gray-300 font-semibold text-lg px-10 py-4">
                Actions
              </TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {organizations.map((organization, index) => (
              <OrganizationRow
                key={organization.id}
                index={index}
                organization={organization}
                onDelete={onDelete}
                balance={organization.balance}
              />
            ))}
          </TableBody>
        </Table>
      </div>
    </div>
  );
}

// organization Row Component
function OrganizationRow({
  organization,
  index,
  onDelete,
}: {
  organization: organization;
  index: number;
  onDelete: (name: string) => void;
}) {
  const router = useRouter();

  return (
    <TableRow>
      <TableCell>{index + 1}</TableCell>
      <TableCell>{organization.name}</TableCell>
      <TableCell>{organization.balance}</TableCell>
      <TableCell className="text-right space-x-2">
        <Button
          variant="outline"
          size="ss"
          onClick={() => {
            sessionStorage.setItem("name", JSON.stringify(organization.name));
            router.push(`/app/settings/organizations/edit/${organization.id}`);
          }}
        >
          Edit
        </Button>
        <DeleteOrganizationDialog
          organization={organization}
          onDelete={onDelete}
        />
      </TableCell>
    </TableRow>
  );
}

// // Add organization Form Component
export function AddOrganizationForm({
  onSubmit,
}: {
  onSubmit: (organization: organization) => void;
}) {
  const [neworganization, setNeworganization] = useState({
    name: "",
  });

  return (
    <form
      onSubmit={(e) => {
        e.preventDefault();
        onSubmit({
          id: Date.now(),
          ...neworganization,
          name: neworganization.name.trim(),
        });
        setNeworganization({
          name: "",
        });
      }}
      className="space-y-4"
    >
      <Input
        placeholder="Organization name"
        value={neworganization.name}
        onChange={(e) =>
          setNeworganization((prev) => ({
            ...prev,
            name: e.target.value,
          }))
        }
      />

      <Button type="submit" className="w-full">
        Add organization
      </Button>
    </form>
  );
}
