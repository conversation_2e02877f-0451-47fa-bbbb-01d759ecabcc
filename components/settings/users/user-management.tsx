// components/settings/users/user-management.tsx
"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { DeleteUserDialog } from "@/app/app/settings/users/page";

export type UserGroup = string;

export interface User {
  id: string;
  username: string;
  email: string;
  group: UserGroup;
  password: string;
  status: string;
}

// User Table Component
export function UserTable({
  users,
  onDelete,
}: {
  users: User[];
  onDelete: (id: string) => void;
}) {
  const router = useRouter();
  return (
    <div className="rounded-lg border bg-card">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead className="w-16">S.N.</TableHead>
            <TableHead>Username</TableHead>
            <TableHead>Email</TableHead>
            <TableHead>Status</TableHead>
            <TableHead>Group</TableHead>
            <TableHead className="text-right">Actions</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {users.length === 0 ? (
            <TableRow>
              <TableCell
                colSpan={6}
                className="text-center py-8 text-muted-foreground"
              >
                No users found
              </TableCell>
            </TableRow>
          ) : (
            users.map((user, index) => (
              <TableRow key={user.id}>
                <TableCell className="font-medium">{index + 1}</TableCell>
                <TableCell className="font-medium">{user.username}</TableCell>
                <TableCell className="text-muted-foreground">
                  {user.email}
                </TableCell>
                <TableCell>
                  <StatusBadge status={user.status} />
                </TableCell>
                <TableCell>
                  <GroupBadge group={user.group} />
                </TableCell>
                <TableCell className="text-right">
                  <div className="flex items-center justify-end gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() =>
                        router.push(`/app/settings/users/edit/${user.id}`)
                      }
                    >
                      Edit
                    </Button>
                    <DeleteUserDialog
                      username={user.username}
                      onDelete={() => onDelete(user.id)}
                    />
                  </div>
                </TableCell>
              </TableRow>
            ))
          )}
        </TableBody>
      </Table>
    </div>
  );
}

// Add User Form Component
export function AddUserForm({ onSubmit }: { onSubmit: (user: User) => void }) {
  const [newUser, setNewUser] = useState({
    username: "",
    email: "",
    password: "",
    confirmPassword: "",
    group: "user" as UserGroup,
  });

  const [passwordStrength, setPasswordStrength] = useState({
    length: false,
    uppercase: false,
    number: false,
  });

  const handlePasswordChange = (password: string) => {
    setPasswordStrength({
      length: password.length >= 8,
      uppercase: /[A-Z]/.test(password),
      number: /\d/.test(password),
    });
    setNewUser((prev) => ({ ...prev, password }));
  };

  const isValid =
    newUser.username.trim() !== "" &&
    passwordStrength.length &&
    passwordStrength.uppercase &&
    passwordStrength.number &&
    newUser.password === newUser.confirmPassword;

  return (
    <form
      onSubmit={(e) => {
        e.preventDefault();
        onSubmit({
          id: Date.now(),
          ...newUser,
          username: newUser.username.trim(),
        });
        setNewUser({
          username: "",
          password: "",
          confirmPassword: "",
          group: "user",
        });
      }}
      className="space-y-4"
    >
      <Input
        placeholder="Username"
        value={newUser.username}
        onChange={(e) =>
          setNewUser((prev) => ({ ...prev, username: e.target.value }))
        }
      />

      <PasswordInput
        value={newUser.password}
        onChange={handlePasswordChange}
        strength={passwordStrength}
      />

      <Input
        type="password"
        placeholder="Confirm Password"
        value={newUser.confirmPassword}
        onChange={(e) =>
          setNewUser((prev) => ({ ...prev, confirmPassword: e.target.value }))
        }
      />

      <groupSelect
        value={newUser.group}
        onChange={(group) => setNewUser((prev) => ({ ...prev, group }))}
      />

      <Button type="submit" className="w-full" disabled={!isValid}>
        Create User
      </Button>
    </form>
  );
}

// Shared Components
function GroupBadge({ group }: { group: UserGroup }) {
  // Generate a consistent variant based on the group name
  const getGroupVariant = (groupName: string) => {
    const variants = ["default", "secondary", "outline"] as const;

    // Simple hash function to get consistent variant for each group
    let hash = 0;
    for (let i = 0; i < groupName.length; i++) {
      hash = ((hash << 5) - hash + groupName.charCodeAt(i)) & 0xffffffff;
    }
    return variants[Math.abs(hash) % variants.length];
  };

  return (
    <Badge variant={getGroupVariant(group)} className="capitalize">
      {group}
    </Badge>
  );
}

function StatusBadge({ status }: { status: string }) {
  const getStatusVariant = (status: string) => {
    switch (status.toLowerCase()) {
      case "active":
        return "default";
      case "inactive":
        return "destructive";
      default:
        return "secondary";
    }
  };

  return (
    <Badge variant={getStatusVariant(status)} className="capitalize">
      {status}
    </Badge>
  );
}

function PasswordInput({
  value,
  onChange,
  strength,
  placeholder = "Password",
}: {
  value: string;
  onChange: (password: string) => void;
  strength: { length: boolean; uppercase: boolean; number: boolean };
  placeholder?: string;
}) {
  return (
    <div className="space-y-2">
      <Input
        type="password"
        placeholder={placeholder}
        value={value}
        onChange={(e) => onChange(e.target.value)}
      />
      <div className="flex gap-2 text-sm">
        <span className={strength.length ? "text-green-600" : "text-gray-500"}>
          • 8+ characters
        </span>
        <span
          className={strength.uppercase ? "text-green-600" : "text-gray-500"}
        >
          • Uppercase
        </span>
        <span className={strength.number ? "text-green-600" : "text-gray-500"}>
          • Number
        </span>
      </div>
    </div>
  );
}
